# Templates Listing Page

## Overview
The Templates page provides a comprehensive listing of all Zalo notification templates with a modern card-based design. Users can view, search, and manage their notification templates efficiently.

## Features

### 1. Template Cards
- **Card Design**: Each template is displayed in a clean, modern card layout
- **Template Information**: Shows template name, Zalo template ID, and data mappings
- **Visual Indicators**: Icons and color coding for better user experience
- **Hover Effects**: Cards have subtle hover animations for better interactivity

### 2. Search and Filtering
- **Real-time Search**: Search templates by name or template ID
- **Instant Results**: Filter results update as you type
- **Search Highlighting**: Clear indication of search terms

### 3. Data Mappings Display
- **Mapping Preview**: Shows first 2 data mappings with field relationships
- **Overflow Indicator**: Shows count of additional mappings when there are more than 2
- **Field Relationships**: Clear visualization of DMS fields to Zalo field mappings

### 4. Action Buttons
- **View**: View detailed template information
- **Edit**: Edit template configuration
- **Delete**: Remove template (with confirmation)
- **Create New**: Add new templates

### 5. Loading and Error States
- **Skeleton Loading**: Animated placeholder cards during data loading
- **Error Handling**: User-friendly error messages with retry options
- **Empty States**: Helpful messages when no templates are found

## Technical Implementation

### GraphQL Query
```graphql
query GetZaloNotificationSettings {
  zaloNotificationSettings(
    where: {
      dmsDataType: {
        eq: NONE
      }
    }
  ) {
    items {
      name
      id
      zaloTemplateId
      zaloNotificationDataMappings {
        dmsFields
        dmsFieldTypes
        zaloField
        zaloFieldFormat
      }
    }
    totalCount
  }
}
```

### Key Components Used
- **Card Components**: `Card`, `CardHeader`, `CardTitle`, `CardDescription`, `CardContent`
- **Input Components**: `Input` for search functionality
- **Button Components**: `Button` with various variants
- **Icons**: Lucide React icons for visual elements
- **Apollo Client**: For GraphQL data fetching with caching

### Responsive Design
- **Mobile First**: Optimized for mobile devices
- **Grid Layout**: Responsive grid that adapts to screen size
  - 1 column on mobile
  - 2 columns on medium screens (md)
  - 3 columns on large screens (lg)
- **Flexible Search**: Search bar adapts to available space

### Navigation Integration
- Added to main navigation menu with "Templates" label
- Uses `List` icon from Lucide React
- Positioned between "HomePage" and "Create Template" for logical flow

## File Structure
```
src/
├── app/
│   └── templates/
│       └── page.tsx              # Main Templates listing page
├── components/
│   ├── Header.tsx                # Updated with Templates navigation
│   └── ui/
│       ├── card.tsx              # Card components (existing)
│       ├── input.tsx             # Input component (existing)
│       └── button.tsx            # Button component (existing)
├── graphql/
│   └── queries.ts                # Updated with enhanced GraphQL query
└── app/
    └── globals.css               # Added line-clamp utilities
```

## Styling Features
- **Tailwind CSS**: Utility-first CSS framework
- **Custom Utilities**: Added line-clamp utilities for text truncation
- **Consistent Design**: Follows existing design system
- **Accessibility**: Proper contrast ratios and keyboard navigation

## Usage
1. Navigate to `/templates` or click "Templates" in the main navigation
2. Use the search bar to find specific templates
3. Click action buttons to view, edit, or delete templates
4. Use "Create New Template" to add new templates
5. Refresh data using the refresh button

## Future Enhancements
- Pagination for large template lists
- Advanced filtering options
- Bulk operations
- Template preview modal
- Export functionality
- Template categories/tags
