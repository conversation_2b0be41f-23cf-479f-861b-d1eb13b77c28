"use client"

import { useState } from "react"
import { useQuery } from "@apollo/client/react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { FileText, Search, Settings, Eye, Edit, Trash2, Plus } from "lucide-react"
import { GET_ZALO_NOTIFICATION_SETTINGS } from "@/graphql/queries"
import router from "next/router"

interface ZaloNotificationDataMapping {
  dmsFields: string
  dmsFieldTypes: string
  zaloField: string
  zaloFieldFormat: string
}

interface ZaloNotificationSetting {
  id: string
  name: string
  zaloTemplateId: string
  zaloNotificationDataMappings: ZaloNotificationDataMapping[]
}

interface ZaloNotificationSettingsResponse {
  zaloNotificationSettings: {
    items: ZaloNotificationSetting[]
    totalCount: number
  }
}

export default function TemplatesPage() {
  const [searchTerm, setSearchTerm] = useState("")
  
  const { data, loading, error, refetch } = useQuery<ZaloNotificationSettingsResponse>(
    GET_ZALO_NOTIFICATION_SETTINGS,
    {
      fetchPolicy: "cache-and-network",
      errorPolicy: "all"
    }
  )

  const templates = data?.zaloNotificationSettings?.items || []
  const totalCount = data?.zaloNotificationSettings?.totalCount || 0

  // Filter templates based on search term
  const filteredTemplates = templates.filter(template =>
    template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    template.zaloTemplateId.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleRefresh = () => {
    refetch()
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <div className="text-red-600 mb-4">Error loading templates</div>
          <p className="text-muted-foreground mb-4">{error.message}</p>
          <Button onClick={handleRefresh} variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Template Listings</h1>
          <p className="text-muted-foreground">
            Manage your Zalo notification templates ({totalCount} total)
          </p>
        </div>
        <Button className="flex items-center gap-2" onClick={() => router.push("/create-template")}>
          <Plus className="h-4 w-4" />
          Create New Template
        </Button>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search templates by name or template ID..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Button variant="outline" onClick={handleRefresh} disabled={loading}>
          <Settings className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {[...Array(6)].map((_, index) => (
            <Card key={index} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-muted rounded w-3/4"></div>
                <div className="h-3 bg-muted rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="h-3 bg-muted rounded"></div>
                  <div className="h-3 bg-muted rounded w-5/6"></div>
                  <div className="h-3 bg-muted rounded w-4/6"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Templates Grid */}
      {!loading && (
        <>
          {filteredTemplates.length === 0 ? (
            <div className="text-center py-12">
              <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No templates found</h3>
              <p className="text-muted-foreground mb-4">
                {searchTerm ? "Try adjusting your search criteria" : "Get started by creating your first template"}
              </p>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Create Template
              </Button>
            </div>
          ) : (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {filteredTemplates.map((template) => (
                <Card key={template.id} className="hover:shadow-lg transition-shadow duration-200">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-lg mb-1 line-clamp-2">
                          {template.name}
                        </CardTitle>
                        <CardDescription className="text-sm">
                          ID: {template.zaloTemplateId}
                        </CardDescription>
                      </div>
                      <FileText className="h-5 w-5 text-primary flex-shrink-0" />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {/* Data Mappings Info */}
                      <div>
                        <h4 className="text-sm font-medium mb-2">Data Mappings</h4>
                        {template.zaloNotificationDataMappings.length > 0 ? (
                          <div className="border rounded-md p-3 bg-muted/30 max-h-32 overflow-y-auto">
                            <div className="space-y-2">
                              {template.zaloNotificationDataMappings.map((mapping, index) => (
                                <div key={index} className="text-xs">
                                  <div className="flex items-center justify-between mb-1">
                                    <span className="font-medium text-foreground truncate flex-1 mr-2">
                                      {mapping.dmsFields}
                                    </span>
                                    <span className="text-primary font-medium">→</span>
                                    <span className="font-medium text-primary truncate flex-1 ml-2">
                                      {mapping.zaloField}
                                    </span>
                                  </div>
                                  <div className="flex text-muted-foreground text-[10px] gap-2">
                                    <span>Type: {mapping.dmsFieldTypes}</span>
                                    {mapping.zaloFieldFormat && (
                                      <span>Format: {mapping.zaloFieldFormat}</span>
                                    )}
                                  </div>
                                  {index < template.zaloNotificationDataMappings.length - 1 && (
                                    <div className="border-b border-border/50 mt-2"></div>
                                  )}
                                </div>
                              ))}
                            </div>
                          </div>
                        ) : (
                          <div className="border rounded-md p-3 bg-muted/30 text-center">
                            <span className="text-xs text-muted-foreground">No data mappings configured</span>
                          </div>
                        )}
                      </div>

                      {/* Action Buttons */}
                      <div className="flex gap-2 pt-2">
                        <Button size="sm" variant="outline" className="flex-1">
                          <Eye className="h-3 w-3 mr-1" />
                          View
                        </Button>
                        <Button size="sm" variant="outline" className="flex-1">
                          <Edit className="h-3 w-3 mr-1" />
                          Edit
                        </Button>
                        <Button size="sm" variant="outline" className="text-destructive hover:text-destructive">
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {/* Results Summary */}
          {filteredTemplates.length > 0 && (
            <div className="mt-8 text-center text-sm text-muted-foreground">
              Showing {filteredTemplates.length} of {totalCount} templates
              {searchTerm && ` matching "${searchTerm}"`}
            </div>
          )}
        </>
      )}
    </div>
  )
}
