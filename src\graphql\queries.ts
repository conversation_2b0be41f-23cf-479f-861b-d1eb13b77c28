import { gql } from "@apollo/client"

export const GET_ZALO_NOTIFICATIONS = gql`
  query GetZaloNotifications($fromDate: DateTime!, $toDate: DateTime!, $skip: Int) {
    zaloNotifications(
      where: {
        createdTime: {
          gte: $fromDate
          lte: $toDate
        }
      },
      order: [{ createdTime: DESC }],
      take: 900,
      skip: $skip
    ) {
      totalCount
      items {
        name
        status
        error
        data
        type
        createdTime
      }
    }
  }
`

export const GET_ZALO_NOTIFICATION_SETTINGS = gql`
  query GetZaloNotificationSettings {
    zaloNotificationSettings(
      where: {
        dmsDataType: {
          eq: NONE
        }
      }
    ) {
      items {
        name
        id
        zaloTemplateId
        zaloNotificationDataMappings {
          dmsFields
          dmsFieldTypes
          zaloField
          zaloFieldFormat
        }
      }
      totalCount
    }
  }
`
